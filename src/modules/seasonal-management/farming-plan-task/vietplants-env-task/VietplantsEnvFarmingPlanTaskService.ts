import Container, { Service } from 'typedi';
import { HttpError } from 'routing-controllers';
import moment from 'moment';
import Logger from '@app/loaders/logger';
import { ICurrentUser } from '@app/interfaces';
import { AssignFarmingPlanTaskDtoArray, AssignFarmingPlanTaskDto, WarehouseItemTaskUsedDto, ProductionQuantityDto, CreateFarmingPlanTaskDto } from '../task/FarmingPlanTask.dto';
import { BulkCreateTaskDto, BulkDeleteTaskDto, TaskTransferDto } from './VietplantsEnvFarmingPlanTask.dto';
import { FilterTuple } from '@app/utils/helpers/queryHelpter';
import { FarmingPlanTaskArray } from '@app/interfaces/ITaskAllResource';
import { FarmingPlanTask } from '@app/orm/entities/farmingPlan/FarmingPlanTask';
import { TaskService } from '@app/modules/farming-plan/task/TaskService';
import { AppDataSource } from '@app/orm/dataSource';
import { In } from 'typeorm';
import { applyQueryFilters } from '@app/utils/helpers/queryHelpter';
import { isVietPlantsTenant } from '@app/utils/vietplants/vietplants-utils';
import { FarmingPlanTaskTemplate, TaskType } from '@app/orm/entities/farmingPlan/template/FarmingPlanTaskTemplate';
import { ItemTaskTemplate } from '@app/orm/entities/farmingPlan/template/ItemTaskTemplate';
import { ProductionTaskTemplate } from '@app/orm/entities/farmingPlan/template/ProductionTaskTemplate';
import { TaskItemTransfer } from '@app/orm/entities/farmingPlan/taskItem/TaskItemTransfer';
import { ProductionQuantity } from '@app/orm/entities/farmingPlan/taskItem/ProductionQuantity';
import { WarehouseItemTaskUsed } from '@app/orm/entities/farmingPlan/taskItem/WarehouseItemTaskUsed';
import { ProductionPlanAllocationService } from '../../production-plan/ProductionPlanAllocationService';
import { ProductionPlanYieldExpectedOutput, ProductionPlan } from '@app/orm/entities/productionPlan/ProductionPlan';
import { TaskItemTransferService } from '../vietplants-item-transfer/TaskItemTransferService';
import { LabelPrintManagementService } from '../vietplants-label-print-management/LabelPrintManagementService';
import { v4 as uuidv4 } from 'uuid';

@Service()
export class FarmingPlanTaskService {
  private oldTaskService: TaskService;
  private taskItemTransferService: TaskItemTransferService;
  private labelPrintManagementService: LabelPrintManagementService;
  private allocationService: ProductionPlanAllocationService;
  private yieldExpectedOutputRepository = AppDataSource.getRepository(ProductionPlanYieldExpectedOutput);
  private productionPlanRepository = AppDataSource.getRepository(ProductionPlan);
  private taskRepo = AppDataSource.getRepository(FarmingPlanTask);
  constructor() {
    this.oldTaskService = Container.get(TaskService);
    this.taskItemTransferService = Container.get(TaskItemTransferService);
    this.labelPrintManagementService = Container.get(LabelPrintManagementService);
    this.allocationService = Container.get(ProductionPlanAllocationService);
  }

  /**
   * Gets a task by its ID
   * @param user Current user
   * @param taskId Task ID
   * @returns FarmingPlanTask
   */
  async getTaskById(user: ICurrentUser, taskId: string): Promise<FarmingPlanTask> {
    try {

      const task = await this.taskRepo.findOne({
        where: { name: taskId },
        relations: ['farmingPlanState', 'assignedUser', 'department', 'environmentTemplate', 'template']
      });

      // Verify the task belongs to the current customer
      if (task && user.customer?.id && task.owner !== user.customer.id.toString()) {
        throw new HttpError(403, `Unauthorized access to task ${taskId}`);
      }

      if (!task) {
        throw new HttpError(404, `Task ${taskId} not found`);
      }

      return task;
    } catch (error) {
      Logger.error(`Error getting task by ID:`, error);
      throw error;
    }
  }


  /**
   * Validates if a task can perform a specific operation based on its type
   * @param user Current user
   * @param taskId Task ID
   * @param requiredType Required task type for the operation
   * @returns Boolean indicating if the operation is allowed
   */
  /**
   * Get task management info with filtering and pagination
   * This implementation mimics the old API using TypeORM
   */
  async getTaskManagementInfo(
    user: ICurrentUser,
    page: number = 1,
    size: number = 10,
    filters?: FilterTuple[],
    stateId?: string,
    templateId?: string,
    status?: string,
    assignedTo?: string,
    taskType?: string,
    orderBy?: string
  ) {
    try {
      const skip = (page - 1) * size;
      const take = size;

      // Create query builder with all necessary joins but select only required fields
      let qb = AppDataSource.getRepository(FarmingPlanTask)
        .createQueryBuilder('task')
        .leftJoin('task.farmingPlanState', 'state')
        .leftJoin('state.farmingPlan', 'farmingPlan')
        .leftJoin('farmingPlan.cropRelation', 'crop')
        .leftJoin('crop.zoneRelation', 'zone')
        .leftJoin('task.template', 'template')
        .leftJoin('task.assignedUser', 'assignedUser')
        .leftJoin('task.department', 'department')
        .leftJoin('task.environmentTemplate', 'environmentTemplate');

      // Select task fields
      qb = qb.select('task');

      // Select only required fields from related entities
      qb = qb.addSelect([
        'state.name', 'state.label',
        'farmingPlan.name',
        'crop.name',
        'crop.label',
        'zone.name', 'zone.customer_id',
        'template.name', 'template.label',
        'assignedUser.name', 'assignedUser.email', 'assignedUser.first_name', 'assignedUser.last_name',
        'department.name', 'department.label',
        'environmentTemplate.name', 'environmentTemplate.label', 'environmentTemplate.environment_code'
      ]);

      // Add VietPlants extension if applicable
      if (user.tenant?.name && isVietPlantsTenant(user.tenant.name)) {
        qb = qb.leftJoin('task.vietplantsExtension', 'vietplantsExtension')
          .addSelect('vietplantsExtension');
      }

      // Add task item transfers information with selective fields
      qb = qb.leftJoin('task.incomingTransfers', 'incomingTransfers')
        .leftJoin('incomingTransfers.sourceTask', 'parentTasks')
        .leftJoin('incomingTransfers.item', 'incomingItems')
        .leftJoin('task.outgoingTransfers', 'outgoingTransfers')
        .leftJoin('outgoingTransfers.targetTask', 'childTasks')
        .leftJoin('outgoingTransfers.item', 'outgoingItems')
        .addSelect([
          'incomingTransfers.name', 'incomingTransfers.source_task_id', 'incomingTransfers.item_id',
          'incomingTransfers.quantity', 'incomingTransfers.uom_id',
          'parentTasks.name',
          'incomingItems.name',
          'outgoingTransfers.name', 'outgoingTransfers.target_task_id',
          'childTasks.name',
          'outgoingItems.name'
        ]);

      // Base conditions
      qb = qb.where('task.deleted IS NULL')
        .andWhere('zone.customer_id = :customer_id', { customer_id: user.customer_id });

      // Apply filters
      if (stateId) {
        qb = qb.andWhere('task.farming_plan_state = :stateId', { stateId });
      }

      if (templateId) {
        qb = qb.andWhere('task.template_id = :templateId', { templateId });
      }

      if (status) {
        qb = qb.andWhere('task.status = :status', { status });
      }

      if (assignedTo) {
        qb = qb.andWhere('task.assigned_to = :assignedTo', { assignedTo });
      }

      if (taskType) {
        qb = qb.andWhere('task.task_type = :taskType', { taskType });
      }

      // Apply dynamic filters
      qb = applyQueryFilters(qb, filters, 'task');

      // Apply ordering
      if (orderBy) {
        const [field, direction] = orderBy.split(':');
        qb = qb.orderBy(`task.${field}`, direction.toUpperCase() as 'ASC' | 'DESC');
      } else {
        // Default ordering
        qb = qb.orderBy('task.creation', 'DESC');
      }

      // Apply pagination
      qb = qb.skip(skip).take(take);

      // Execute query
      const [tasks, total] = await qb.getManyAndCount();

      // Process tasks to include chain information and related production/warehouse items
      const processedTasks = await Promise.all(tasks.map(async (task) => {
        // Get parent and child task counts for the new multi-parent model
        const parentTaskCount = await AppDataSource.getRepository(TaskItemTransfer)
          .createQueryBuilder('transfer')
          .where('transfer.target_task_id = :taskId', { taskId: task.name })
          .getCount();

        const childTaskCount = await AppDataSource.getRepository(TaskItemTransfer)
          .createQueryBuilder('transfer')
          .where('transfer.source_task_id = :taskId', { taskId: task.name })
          .getCount();

        // Group item transfers by parent task
        const itemsByParent: Record<string, any[]> = {};
        if (task.incomingTransfers && task.incomingTransfers.length > 0) {
          task.incomingTransfers.forEach(transfer => {
            if (!itemsByParent[transfer.source_task_id]) {
              itemsByParent[transfer.source_task_id] = [];
            }
            itemsByParent[transfer.source_task_id].push({
              itemId: transfer.item_id,
              itemName: transfer.item?.name,
              quantity: transfer.quantity,
              uomId: transfer.uom_id
            });
          });
        }

        // Legacy chain support - include previous chain information if available
        let legacyChainTasks: FarmingPlanTask[] = [];
        if (task.previous_task_id) {
          try {
            // For now, just get the previous task directly since getTaskChain method doesn't exist
            const previousTask = await this.taskRepo.findOne({
              where: { name: task.previous_task_id }
            });
            if (previousTask) {
              legacyChainTasks = [previousTask];
            }
          } catch (error) {
            Logger.error(`Error getting chain for task ${task.name}:`, error);
          }
        }

        // Get production quantities related to this task
        const productionQuantities = await AppDataSource.getRepository(ProductionQuantity)
          .createQueryBuilder('prodQty')
          .leftJoinAndSelect('prodQty.product', 'product')
          .leftJoinAndSelect('prodQty.activeUOM', 'activeUOM')
          .where('prodQty.task_id = :taskId', { taskId: task.name })
          .andWhere('prodQty.deleted IS NULL')
          .getMany();

        // Get warehouse items used in this task
        const warehouseItemsUsed = await AppDataSource.getRepository(WarehouseItemTaskUsed)
          .createQueryBuilder('warehouseItem')
          .leftJoinAndSelect('warehouseItem.iotCategory', 'iotCategory')
          .leftJoinAndSelect('warehouseItem.activeUOM', 'activeUOM')
          .where('warehouseItem.task_id = :taskId', { taskId: task.name })
          .andWhere('warehouseItem.deleted IS NULL')
          .getMany();

        // Return enhanced task with chaining information and related items
        return {
          ...task,
          taskChainInfo: {
            parentTaskCount,
            childTaskCount,
            itemsByParent,
            hasLegacyChain: !!task.previous_task_id || !!task.task_chain_ids,
            legacyChainTasks: legacyChainTasks
          },
          productionInfo: {
            items: productionQuantities.map(pq => ({
              name: pq.name,
              product_id: pq.product_id,
              product_name: pq.product?.name,
              product_label: pq.product?.label,
              quantity: pq.quantity,
              exp_quantity: pq.exp_quantity,
              finished_quantity: pq.finished_quantity,
              lost_quantity: pq.lost_quantity,
              draft_quantity: pq.draft_quantity,
              issued_quantity: pq.issued_quantity,
              total_qty_in_crop: pq.total_qty_in_crop,
              active_uom: pq.active_uom,
              uom_name: pq.activeUOM?.name,
              active_conversion_factor: pq.active_conversion_factor,
              description: pq.description
            })),
            total_items: productionQuantities.length
          },
          warehouseItemsInfo: {
            items: warehouseItemsUsed.map(wi => ({
              name: wi.name,
              iot_category_id: wi.iot_category_id,
              category_name: wi.iotCategory?.name,
              category_label: wi.iotCategory?.label,
              quantity: wi.quantity,
              exp_quantity: wi.exp_quantity,
              loss_quantity: wi.loss_quantity,
              issued_quantity: wi.issued_quantity,
              draft_quantity: wi.draft_quantity,
              total_qty_in_crop: wi.total_qty_in_crop,
              active_uom: wi.active_uom,
              uom_name: wi.activeUOM?.name,
              active_conversion_factor: wi.active_conversion_factor,
              description: wi.description
            })),
            total_items: warehouseItemsUsed.length
          }
        };
      }));

      return {
        data: processedTasks,
        page,
        size,
        total
      };
    } catch (error) {
      Logger.error('Error getting task management info:', error);
      throw error;
    }
  }

  /**
   * Update a task with partial data
   * Any field included in the request body will be updated
   * @param user Current user
   * @param taskId Task ID to update
   * @param updateData Partial data to update
   * @returns Updated task
   */
  async updateTask(user: ICurrentUser, taskId: string, updateData: any): Promise<FarmingPlanTask> {
    try {
      // Get the existing task
      const task = await this.getTaskById(user, taskId);

      // Prevent task from linking to itself
      if (updateData.previous_task_id === taskId) {
        throw new HttpError(400, `Cannot link a task to itself`);
      }

      // Store previous task ID before update
      const oldPreviousTaskId = task.previous_task_id;

      // Handle chain linking if previous_task_id has changed
      if (updateData.previous_task_id !== undefined && updateData.previous_task_id !== oldPreviousTaskId) {
        // Double-check to prevent self-reference
        if (updateData.previous_task_id === taskId) {
          throw new HttpError(400, `Cannot link a task to itself`);
        }

        // Create a clean copy of update data without chain-related fields
        const cleanUpdateData = { ...updateData };
        delete cleanUpdateData.previous_task_id;
        delete cleanUpdateData.task_chain_ids;

        // Remove all relation fields and computed properties
        this.cleanUpdateDataObject(cleanUpdateData);

        // Update the basic task data first
        const repo = AppDataSource.getRepository(FarmingPlanTask);

        if (Object.keys(cleanUpdateData).length > 0) {
          await repo.createQueryBuilder()
            .update(FarmingPlanTask)
            .set(cleanUpdateData)
            .where("name = :taskId", { taskId })
            .execute();
        }

        if (updateData.previous_task_id && updateData.previous_task_id.trim() !== '') {
          // Link to a new previous task
          try {
            // Update the previous_task_id directly since updateTaskChain method doesn't exist
            await repo.createQueryBuilder()
              .update(FarmingPlanTask)
              .set({
                previous_task_id: updateData.previous_task_id
              })
              .where("name = :taskId", { taskId })
              .execute();

            // Fetch the updated task with all relations
            return await this.getTaskById(user, taskId);
          } catch (chainError) {
            Logger.error(`Error updating task chain:`, chainError);
            throw chainError; // Re-throw to prevent invalid state
          }
        } else {
          // Remove from chain (previous_task_id set to empty string)
          // Use direct SQL update to avoid any TypeORM relation issues
          await repo.createQueryBuilder()
            .update(FarmingPlanTask)
            .set({
              previous_task_id: '',
              task_chain_ids: ''
            })
            .where("name = :taskId", { taskId })
            .execute();

          // Fetch the updated task with all relations
          return await this.getTaskById(user, taskId);
        }
      }

      // Save the updated task using direct SQL update
      // We need to exclude relation fields that can't be updated via QueryBuilder
      const repo = AppDataSource.getRepository(FarmingPlanTask);

      // Create a clean copy without relation fields and computed properties
      const cleanUpdateData = { ...updateData };
      this.cleanUpdateDataObject(cleanUpdateData);

      // Only update if there are fields to update
      if (Object.keys(cleanUpdateData).length > 0) {
        await repo.createQueryBuilder()
          .update(FarmingPlanTask)
          .set(cleanUpdateData)
          .where("name = :taskId", { taskId })
          .execute();
      }

      // Reload the task with all relations
      return await this.getTaskById(user, taskId);
    } catch (error) {
      Logger.error(`Error updating task:`, error);
      throw error;
    }
  }

  /**
   * Helper method to clean update data object by removing relation fields and computed properties
   * that can't be directly updated through TypeORM's QueryBuilder
   * @param updateData Object to clean
   */
  private cleanUpdateDataObject(updateData: any): void {
    if (!updateData) return;

    // Remove relation fields that can't be updated via QueryBuilder
    delete updateData.nextTasks;
    delete updateData.previousTask;
    delete updateData.farmingPlanState;
    delete updateData.assignedUser;
    delete updateData.department;
    delete updateData.environmentTemplate;
    delete updateData.template;
    delete updateData.vietplantsExtension;

    // Remove the newly added transfer relations
    delete updateData.incomingTransfers;
    delete updateData.outgoingTransfers;

    // Remove computed properties added by our API
    delete updateData.taskChainInfo;
    delete updateData.productionInfo;
    delete updateData.warehouseItemsInfo;
  }

  /**
   * Create a new farming plan task
   */
  async createTask(user: ICurrentUser, createData: CreateFarmingPlanTaskDto): Promise<FarmingPlanTask> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);

      // Generate unique task name if not provided
      if (!createData.name) {
        createData.name = uuidv4();
      }

      // Set default values
      const taskData = {
        ...createData,
        created: new Date(),
        created_by: user.user_id,
        modified: new Date(),
        modified_by: user.user_id,
      };

      const task = repo.create(taskData as any);
      const savedTask = await repo.save(task);

      // Handle both single task and array return types
      const taskName = Array.isArray(savedTask) ? (savedTask[0] as any).name : (savedTask as any).name;
      return await this.getTaskById(user, taskName);
    } catch (error) {
      Logger.error(`Error creating task:`, error);
      throw error;
    }
  }

  /**
   * Create an ENV_POUR task with task transfers
   */
  async createEnvPourTask(user: ICurrentUser, createData: CreateFarmingPlanTaskDto & { task_transfers?: TaskTransferDto[] }): Promise<FarmingPlanTask> {
    try {
      // Validate task type
      if ((createData.task_type as string) !== 'ENV_POUR') {
        throw new Error('This method is only for ENV_POUR tasks');
      }

      // Validate task transfers if provided
      if (createData.task_transfers && createData.task_transfers.length > 0) {
        await this.validateTaskTransfers(user, createData.task_transfers);
      }

      // Create the main task first
      const task = await this.createTask(user, createData as any);

      // Create task transfers if provided
      if (createData.task_transfers && createData.task_transfers.length > 0) {
        await this.createTaskTransfers(user, task.name, createData.task_transfers);
      }

      return await this.getTaskById(user, task.name);
    } catch (error) {
      Logger.error(`Error creating ENV_POUR task:`, error);
      throw error;
    }
  }

  /**
   * Bulk create tasks
   */
  async bulkCreateTasks(user: ICurrentUser, bulkData: BulkCreateTaskDto): Promise<FarmingPlanTask[]> {
    try {
      const results: FarmingPlanTask[] = [];

      for (const taskData of bulkData.tasks) {
        const task = await this.createTask(user, taskData as any);
        results.push(task);
      }

      return results;
    } catch (error) {
      Logger.error(`Error bulk creating tasks:`, error);
      throw error;
    }
  }

  /**
   * Delete a task by ID
   */
  async deleteTask(user: ICurrentUser, taskId: string): Promise<{ success: boolean; message: string }> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);

      // Check if task exists
      const task = await repo.findOne({ where: { name: taskId } });
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      // Delete related transfers first
      await this.deleteTaskTransfers(user, taskId);

      // Delete the task
      await repo.delete({ name: taskId });

      return {
        success: true,
        message: `Task ${taskId} deleted successfully`
      };
    } catch (error) {
      Logger.error(`Error deleting task:`, error);
      throw error;
    }
  }

  /**
   * Bulk delete tasks
   */
  async bulkDeleteTasks(user: ICurrentUser, bulkData: BulkDeleteTaskDto): Promise<{ success: boolean; deleted_count: number; message: string }> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);
      let deletedCount = 0;

      for (const taskId of bulkData.task_ids) {
        try {
          await this.deleteTask(user, taskId);
          deletedCount++;
        } catch (error) {
          Logger.warn(`Failed to delete task ${taskId}:`, error);
        }
      }

      return {
        success: true,
        deleted_count: deletedCount,
        message: `Successfully deleted ${deletedCount} out of ${bulkData.task_ids.length} tasks`
      };
    } catch (error) {
      Logger.error(`Error bulk deleting tasks:`, error);
      throw error;
    }
  }

  /**
   * Get available source tasks for ENV_POUR task linking
   * Returns ENV_STOCK and ENV_STEAM_POT tasks that can be linked
   */
  async getAvailableSourceTasks(user: ICurrentUser, stateId?: string, departmentId?: string): Promise<FarmingPlanTask[]> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);
      const queryBuilder = repo.createQueryBuilder('task');

      // Filter for ENV_STOCK and ENV_STEAM_POT tasks
      queryBuilder.where('task.task_type IN (:...taskTypes)', {
        taskTypes: ['ENV_STOCK', 'ENV_STEAM_POT']
      });

      // Add optional filters
      if (stateId) {
        queryBuilder.andWhere('task.farming_plan_state = :stateId', { stateId });
      }

      if (departmentId) {
        queryBuilder.andWhere('task.department_id = :departmentId', { departmentId });
      }

      // Order by creation date
      queryBuilder.orderBy('task.created', 'DESC');

      return await queryBuilder.getMany();
    } catch (error) {
      Logger.error(`Error getting available source tasks:`, error);
      throw error;
    }
  }

  /**
   * Validate task transfers for ENV_POUR tasks
   */
  private async validateTaskTransfers(user: ICurrentUser, transfers: TaskTransferDto[]): Promise<void> {
    const repo = AppDataSource.getRepository(FarmingPlanTask);

    for (const transfer of transfers) {
      // Check if source task exists
      const sourceTask = await repo.findOne({
        where: { name: transfer.source_task_id }
      });

      if (!sourceTask) {
        throw new Error(`Source task ${transfer.source_task_id} not found`);
      }

      // Validate source task type
      if (!['ENV_STOCK', 'ENV_STEAM_POT'].includes(transfer.source_task_type as string)) {
        throw new Error(`Invalid source task type: ${transfer.source_task_type}. Only ENV_STOCK and ENV_STEAM_POT are allowed`);
      }

      // Verify the source task type matches
      if ((sourceTask.task_type as string) !== (transfer.source_task_type as string)) {
        throw new Error(`Source task type mismatch: expected ${transfer.source_task_type}, got ${sourceTask.task_type}`);
      }
    }
  }

  /**
   * Create task transfers for ENV_POUR tasks
   * Note: This creates a simple task link without specific item transfers
   * For actual item transfers, use TaskItemTransferService
   */
  private async createTaskTransfers(user: ICurrentUser, targetTaskId: string, transfers: TaskTransferDto[]): Promise<void> {
    const transferRepo = AppDataSource.getRepository(TaskItemTransfer);

    for (const transfer of transfers) {
      // Create a placeholder transfer record to establish task linking
      // In a real scenario, you would specify actual items being transferred
      const transferEntity = new TaskItemTransfer();
      transferEntity.name = uuidv4();
      transferEntity.source_task_id = transfer.source_task_id;
      transferEntity.target_task_id = targetTaskId;
      transferEntity.item_id = 'PLACEHOLDER_ITEM'; // This should be replaced with actual item IDs
      transferEntity.quantity = 0; // Placeholder quantity
      transferEntity.conversion_factor = 1;
      transferEntity.description = transfer.description || `Task link from ${transfer.source_task_type} to ENV_POUR`;
      transferEntity.transfer_date = new Date();
      transferEntity.transfer_by = user.user_id;
      transferEntity.status = 'Linked';

      // Set ownership if available
      if (user.customer?.id) {
        transferEntity.owner = user.customer.id.toString();
      }

      await transferRepo.save(transferEntity);
    }
  }

  /**
   * Delete task transfers for a task
   */
  private async deleteTaskTransfers(user: ICurrentUser, taskId: string): Promise<void> {
    const transferRepo = AppDataSource.getRepository(TaskItemTransfer);

    // Delete both incoming and outgoing transfers
    await transferRepo.delete({ source_task_id: taskId });
    await transferRepo.delete({ target_task_id: taskId });
  }

}